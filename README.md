# DeepSeek SpringAI Family Doctor

一个基于Spring Boot + Spring AI的智能家庭医生应用，支持本地Ollama模型和DeepSeek在线模型。

## 功能特性

- 🤖 AI医生咨询：专业的医疗建议和健康咨询
- 🔄 多模型支持：本地Ollama模型 + DeepSeek在线模型
- 💬 多种聊天模式：同步、异步、流式响应
- 📡 实时通信：基于SSE的实时消息推送
- 💾 聊天记录：MySQL数据库存储聊天历史
- 🎭 角色扮演：AI扮演专业医生"风间影月"

## 快速开始

### 1. 本地Ollama模型设置
```bash
ollama create my-doctor:0.1 -f my_doctor_0_1
```

### 2. DeepSeek在线模型设置
```bash
# 设置环境变量
export DEEPSEEK_API_KEY=your-deepseek-api-key

# 或在application-dev.yml中配置
spring:
  ai:
    openai:
      api-key: your-deepseek-api-key
```

### 3. 启动应用
```bash
mvn spring-boot:run
```

## API接口

### 统一接口（推荐）
- `GET /ai/chat?msg=你好&modelType=deepseek` - 统一聊天
- `POST /ai/doctor/stream` - 统一医生咨询
- `GET /ai/models` - 获取支持的模型

### 专用接口
- Ollama: `/ollama/ai/*`
- DeepSeek: `/deepseek/ai/*`

详细配置请参考：`src/main/resources/deepseek-config.md`