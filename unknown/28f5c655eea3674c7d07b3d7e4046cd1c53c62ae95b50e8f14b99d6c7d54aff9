<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itzixi.mapper.ChatRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.itzixi.bean.ChatRecord">
        <id column="id" property="id" />
        <result column="content" property="content" />
        <result column="chat_type" property="chatType" />
        <result column="chat_time" property="chatTime" />
        <result column="family_member" property="familyMember" />
    </resultMap>

    <!-- 通用查询结果列 -->
<!--    <sql id="Base_Column_List">-->
<!--        id, content, chat_type, chat_time, family_member-->
<!--    </sql>-->

</mapper>