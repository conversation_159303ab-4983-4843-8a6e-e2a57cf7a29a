server:
  port: 9090      # 当前项目所在服务的端口号

# 配置日志级别为info
logging:
  level:
    root: info

spring:
  datasource:                                     # 数据源的相关配置
    type: com.zaxxer.hikari.HikariDataSource      # 数据源的类型，可以更改为其他的数据源配置，比如druid
    driver-class-name: com.mysql.cj.jdbc.Driver      # mysql/MariaDB 的数据库驱动类名称
    url: ***********************************************************************************************************************************************************************
    username: root
    password: Abc_123456789
    hikari:
      pool-name: DataSourceHikariCP           # 连接池的名字
      connection-timeout: 30000               # 等待连接池分配连接的最大时间（毫秒），超过这个时长还没有可用的连接，则会抛出SQLException
      minimum-idle: 5                         # 最小连接数
      maximum-pool-size: 20                   # 最大连接数
      auto-commit: true                       # 自动提交
      idle-timeout: 600000                    # 连接超时的最大时长（毫秒），超时则会被释放（retired）
      max-lifetime: 18000000                  # 连接池的最大生命时长（毫秒），超时则会被释放（retired）
      connection-test-query: SELECT 1
  ai:
    ollama:
      base-url: http://127.0.0.1:11434
      chat:
        model: my-doctor:1.0.2.RELEASE
    openai:
      api-key: ${DEEPSEEK_API_KEY:your-deepseek-api-key-here}
      base-url: https://api.deepseek.com
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7


website:
  domain: http://150.109.247.64