# DeepSeek 集成配置说明

## 项目概述
本项目已成功集成 DeepSeek 在线模型，与原有的 Ollama 本地模型并存，提供统一的 AI 服务接口。

## 1. 获取 DeepSeek API Key
1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在控制台中创建 API Key

## 2. 配置 API Key
在 `src/main/resources/application.yml` 中配置你的 API Key：

```yaml
# DeepSeek AI 配置
deepseek:
  api:
    key: your-actual-api-key-here  # 替换为你的真实API密钥
    url: https://api.deepseek.com/v1/chat/completions
  model: deepseek-chat
```

## 3. 可用接口

### DeepSeek 专用接口
- **测试接口**: `GET /deepseek/test`
- **同步聊天**: `POST /deepseek/chat?message=你好`
- **异步聊天**: `POST /deepseek/chat/async?message=你好`
- **流式聊天**: `POST /deepseek/chat/stream?message=你好`
- **带用户名聊天（保存数据库）**: `POST /deepseek/chat/withUser`
- **带用户名异步聊天（保存数据库）**: `POST /deepseek/chat/async/withUser`
- **带用户名流式聊天（保存数据库）**: `POST /deepseek/chat/stream/withUser`
- **获取聊天记录**: `GET /deepseek/getRecords?who=用户名`

### Ollama 专用接口
- **测试接口**: `GET /ollama/test`
- **同步聊天**: `POST /ollama/chat?message=你好`
- **流式聊天**: `POST /ollama/stream1?message=你好`
- **医生咨询（SSE）**: `POST /ollama/ai/v3/doctor/stream`
- **获取聊天记录**: `GET /ollama/getRecords?who=用户名`

### 统一 AI 接口（推荐使用）
- **测试接口**: `GET /ai/test`
- **同步聊天**: `POST /ai/chat?message=你好&model=deepseek`
- **同步聊天**: `POST /ai/chat?message=你好&model=ollama`
- **异步聊天**: `POST /ai/chat/async?message=你好&model=deepseek`
- **流式聊天**: `POST /ai/chat/stream?message=你好&model=deepseek`
- **带用户名聊天（保存数据库）**: `POST /ai/chat/withUser`
- **带用户名异步聊天（保存数据库）**: `POST /ai/chat/async/withUser`
- **带用户名流式聊天（保存数据库）**: `POST /ai/chat/stream/withUser`
- **获取聊天记录**: `GET /ai/getRecords?who=用户名`

## 4. 数据库存储功能

### ChatEntity 请求格式
```json
{
    "currentUserName": "张三",
    "message": "我最近总是头疼，是什么原因？",
    "modelType": "deepseek"  // 可选，默认为deepseek
}
```

### 聊天记录存储
- 用户发送的消息会自动保存到数据库，类型为 "user"
- AI回复的消息会自动保存到数据库，类型为 "bot"
- 可通过 `/getRecords?who=用户名` 查询历史记录

## 5. 模型特点

### DeepSeek 模型
- **类型**: 在线模型
- **优势**: 响应速度快，模型能力强
- **需要**: 网络连接和 API Key
- **医生角色**: 风间影月家庭医生
- **输出格式**: HTML 格式，便于前端展示
- **数据库存储**: ✅ 支持

### Ollama 模型
- **类型**: 本地模型
- **优势**: 无需网络，数据隐私性好
- **需要**: 本地 Ollama 服务运行
- **医生角色**: 自定义医生模型
- **数据库存储**: ✅ 支持

## 6. 启动应用
```bash
mvn spring-boot:run
```

## 7. 测试示例
```bash
# 测试 DeepSeek（带数据库存储）
curl -X POST "http://localhost:8080/deepseek/chat/withUser" \
  -H "Content-Type: application/json" \
  -d '{"currentUserName":"张三","message":"我头疼怎么办"}'

# 测试统一接口（带数据库存储）
curl -X POST "http://localhost:8080/ai/chat/withUser" \
  -H "Content-Type: application/json" \
  -d '{"currentUserName":"张三","message":"我头疼怎么办","modelType":"deepseek"}'

# 获取聊天记录
curl "http://localhost:8080/ai/getRecords?who=张三"
```

## 8. 注意事项
- 确保 DeepSeek API Key 有效且有足够的配额
- Ollama 服务需要单独启动（如果使用本地模型）
- 统一接口支持动态切换模型，便于对比不同模型的效果
- 所有带 `withUser` 的接口都会自动保存聊天记录到数据库
- 数据库配置请参考 `application-dev.yml` 或 `application-prod.yml`
