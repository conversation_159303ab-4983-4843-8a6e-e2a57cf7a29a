# DeepSeek在线模型配置说明

## 1. 获取DeepSeek API Key

1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册并登录账户
3. 进入API管理页面
4. 创建新的API Key
5. 复制API Key备用

## 2. 配置环境变量

### 方式一：系统环境变量
```bash
export DEEPSEEK_API_KEY=your-actual-deepseek-api-key
```

### 方式二：IDE环境变量
在IDE中设置环境变量：
- IntelliJ IDEA: Run/Debug Configurations -> Environment variables
- Eclipse: Run Configurations -> Environment

### 方式三：直接修改配置文件
修改 `application-dev.yml` 或 `application-prod.yml` 中的：
```yaml
spring:
  ai:
    openai:
      api-key: sk-your-actual-deepseek-api-key
```

## 3. API接口说明

### DeepSeek专用接口
- `GET /deepseek/ai/chat?msg=你好` - 同步聊天
- `GET /deepseek/ai/stream1?msg=你好` - 流式聊天(Flux)
- `GET /deepseek/ai/stream2?msg=你好` - 流式聊天(List)
- `POST /deepseek/ai/doctor/stream` - 医生咨询(SSE)
- `GET /deepseek/test` - 连接测试

### 统一接口（支持模型切换）
- `GET /ai/chat?msg=你好&modelType=deepseek` - 统一聊天
- `GET /ai/stream1?msg=你好&modelType=deepseek` - 统一流式聊天
- `POST /ai/doctor/stream` - 统一医生咨询（在请求体中指定modelType）
- `GET /ai/models` - 获取支持的模型列表

## 4. 请求示例

### 统一医生咨询接口
```json
POST /ai/doctor/stream
{
    "currentUserName": "张三",
    "message": "我最近总是头疼，是什么原因？",
    "modelType": "deepseek"
}
```

## 5. 注意事项

1. DeepSeek API需要网络连接，确保服务器可以访问外网
2. API调用会产生费用，请合理使用
3. 建议在生产环境中使用环境变量配置API Key
4. 如果不指定modelType，默认使用ollama本地模型
