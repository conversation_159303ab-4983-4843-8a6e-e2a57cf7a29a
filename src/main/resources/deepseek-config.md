# DeepSeek 集成配置说明

## 项目概述
本项目已成功集成 DeepSeek 在线模型，与原有的 Ollama 本地模型并存，提供统一的 AI 服务接口。

## 1. 获取 DeepSeek API Key
1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在控制台中创建 API Key

## 2. 配置 API Key
在 `src/main/resources/application.yml` 中配置你的 API Key：

```yaml
# DeepSeek AI 配置
deepseek:
  api:
    key: your-actual-api-key-here  # 替换为你的真实API密钥
    url: https://api.deepseek.com/v1/chat/completions
  model: deepseek-chat
```

## 3. 可用接口

### DeepSeek 专用接口
- **测试接口**: `GET /deepseek/test`
- **同步聊天**: `POST /deepseek/chat?message=你好`
- **异步聊天**: `POST /deepseek/chat/async?message=你好`
- **流式聊天**: `POST /deepseek/chat/stream?message=你好`
spring:
  ai:
    openai:
      api-key: sk-your-actual-deepseek-api-key
```

## 3. API接口说明

### DeepSeek专用接口
- `GET /deepseek/ai/chat?msg=你好` - 同步聊天
- `GET /deepseek/ai/stream1?msg=你好` - 流式聊天(Flux)
- `GET /deepseek/ai/stream2?msg=你好` - 流式聊天(List)
- `POST /deepseek/ai/doctor/stream` - 医生咨询(SSE)
- `GET /deepseek/test` - 连接测试

### 统一接口（支持模型切换）
- `GET /ai/chat?msg=你好&modelType=deepseek` - 统一聊天
- `GET /ai/stream1?msg=你好&modelType=deepseek` - 统一流式聊天
- `POST /ai/doctor/stream` - 统一医生咨询（在请求体中指定modelType）
- `GET /ai/models` - 获取支持的模型列表

## 4. 请求示例

### 统一医生咨询接口
```json
POST /ai/doctor/stream
{
    "currentUserName": "张三",
    "message": "我最近总是头疼，是什么原因？",
    "modelType": "deepseek"
}
```

## 5. 注意事项

1. DeepSeek API需要网络连接，确保服务器可以访问外网
2. API调用会产生费用，请合理使用
3. 建议在生产环境中使用环境变量配置API Key
4. 如果不指定modelType，默认使用ollama本地模型
