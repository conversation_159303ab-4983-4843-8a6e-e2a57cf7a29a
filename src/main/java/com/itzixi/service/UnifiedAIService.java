package com.itzixi.service;

import org.springframework.ai.chat.ChatResponse;
import reactor.core.publisher.Flux;

/**
 * 统一AI服务接口，支持多种AI模型
 */
public interface UnifiedAIService {

    /**
     * 同步聊天
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 聊天响应
     */
    ChatResponse chat(String message, String modelType);

    /**
     * 异步聊天
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 聊天响应
     */
    ChatResponse chatAsync(String message, String modelType);

    /**
     * 流式聊天
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 流式响应
     */
    Flux<ChatResponse> chatStream(String message, String modelType);
}
