package com.itzixi.service;

import org.springframework.ai.chat.ChatResponse;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @ClassName UnifiedAIService
 * <AUTHOR>
 * @Version 1.0
 * @Description 统一AI服务接口，支持多模型切换
 **/
public interface UnifiedAIService {

    /**
     * 统一聊天接口 - 同步
     * @param msg 用户消息
     * @param modelType 模型类型：ollama 或 deepseek
     * @return AI回复
     */
    Object aiChat(String msg, String modelType);

    /**
     * 统一流式聊天接口 - 返回Flux
     * @param msg 用户消息
     * @param modelType 模型类型：ollama 或 deepseek
     * @return 流式响应
     */
    Flux<ChatResponse> aiStream1(String msg, String modelType);

    /**
     * 统一流式聊天接口 - 返回List
     * @param msg 用户消息
     * @param modelType 模型类型：ollama 或 deepseek
     * @return 响应列表
     */
    List<String> aiStream2(String msg, String modelType);

    /**
     * 统一医生流式聊天接口 - 带SSE推送
     * @param userName 用户名
     * @param message 用户消息
     * @param modelType 模型类型：ollama 或 deepseek
     */
    void doDoctorStream(String userName, String message, String modelType);
}
