package com.itzixi.service;

import org.springframework.ai.chat.ChatResponse;
import reactor.core.publisher.Flux;

/**
 * 统一AI服务接口，支持多种AI模型
 */
public interface UnifiedAIService {

    /**
     * 同步聊天
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 聊天响应
     */
    ChatResponse chat(String message, String modelType);

    /**
     * 异步聊天
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 聊天响应
     */
    ChatResponse chatAsync(String message, String modelType);

    /**
     * 流式聊天
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 流式响应
     */
    Flux<ChatResponse> chatStream(String message, String modelType);

    /**
     * 带用户名的聊天（会保存到数据库）
     * @param userName 用户名
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 聊天响应
     */
    ChatResponse chatWithUser(String userName, String message, String modelType);

    /**
     * 带用户名的异步聊天（会保存到数据库）
     * @param userName 用户名
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 聊天响应
     */
    ChatResponse chatAsyncWithUser(String userName, String message, String modelType);

    /**
     * 带用户名的流式聊天（会保存到数据库）
     * @param userName 用户名
     * @param message 用户消息
     * @param modelType 模型类型 (ollama/deepseek)
     * @return 流式响应
     */
    Flux<ChatResponse> chatStreamWithUser(String userName, String message, String modelType);
}
