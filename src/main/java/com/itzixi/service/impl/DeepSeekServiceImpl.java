package com.itzixi.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itzixi.service.ChatRecordService;
import com.itzixi.service.DeepSeekService;
import com.itzixi.utils.ChatTypeEnum;
import com.itzixi.utils.SSEMsgType;
import com.itzixi.utils.SSEServer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.Generation;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * DeepSeek AI 服务实现
 */
@Slf4j
@Service
public class DeepSeekServiceImpl implements DeepSeekService {

    @Value("${deepseek.api.key:your-api-key-here}")
    private String apiKey;

    @Value("${deepseek.api.url:https://api.deepseek.com/v1/chat/completions}")
    private String apiUrl;

    @Value("${deepseek.model:deepseek-chat}")
    private String model;

    @Resource
    private ChatRecordService chatRecordService;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public DeepSeekServiceImpl() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public ChatResponse chat(String message) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = buildRequestBody(message, false);
            
            // 发送请求
            String response = sendRequest(requestBody);
            
            // 解析响应
            return parseResponse(response);
            
        } catch (Exception e) {
            log.error("DeepSeek chat error: ", e);
            throw new RuntimeException("DeepSeek API调用失败: " + e.getMessage());
        }
    }

    @Override
    public ChatResponse chatAsync(String message) {
        // 对于简单实现，异步和同步使用相同逻辑
        return chat(message);
    }

    @Override
    public Flux<ChatResponse> chatStream(String message) {
        // 简单实现：返回单个响应的Flux
        return Flux.just(chat(message));
    }

    @Override
    public ChatResponse chatWithUser(String userName, String message) {
        log.info("DeepSeek带用户名聊天 - 用户: {}, 消息: {}", userName, message);

        // 保存用户发送的记录到数据库
        chatRecordService.saveChatRecord(userName, message, ChatTypeEnum.USER);

        try {
            // 调用DeepSeek API
            ChatResponse response = chat(message);

            // 获取AI回复内容
            String aiResponse = response.getResult().getOutput().getContent();

            // 保存AI回复的记录到数据库
            chatRecordService.saveChatRecord(userName, aiResponse, ChatTypeEnum.BOT);

            log.info("DeepSeek聊天完成 - 用户: {}, AI回复长度: {}", userName, aiResponse.length());

            return response;

        } catch (Exception e) {
            log.error("DeepSeek聊天失败 - 用户: {}, 错误: {}", userName, e.getMessage());
            // 保存错误信息到数据库
            chatRecordService.saveChatRecord(userName, "抱歉，AI服务暂时不可用：" + e.getMessage(), ChatTypeEnum.BOT);
            throw e;
        }
    }

    @Override
    public ChatResponse chatAsyncWithUser(String userName, String message) {
        // 对于简单实现，异步和同步使用相同逻辑
        return chatWithUser(userName, message);
    }

    @Override
    public Flux<ChatResponse> chatStreamWithUser(String userName, String message) {
        log.info("DeepSeek流式聊天 - 用户: {}, 消息: {}", userName, message);

        // 保存用户发送的记录到数据库
        chatRecordService.saveChatRecord(userName, message, ChatTypeEnum.USER);

        try {
            // 获取响应
            ChatResponse response = chat(message);
            String aiResponse = response.getResult().getOutput().getContent();

            // 保存AI回复的记录到数据库
            chatRecordService.saveChatRecord(userName, aiResponse, ChatTypeEnum.BOT);

            return Flux.just(response);

        } catch (Exception e) {
            log.error("DeepSeek流式聊天失败 - 用户: {}, 错误: {}", userName, e.getMessage());
            // 保存错误信息到数据库
            chatRecordService.saveChatRecord(userName, "抱歉，AI服务暂时不可用：" + e.getMessage(), ChatTypeEnum.BOT);
            return Flux.error(e);
        }
    }


    @Override
    public void deepSeekDoctorSSE(String userName, String message) {
        log.info("DeepSeek医生SSE流式响应 - 用户: {}, 消息: {}", userName, message);

        // 保存用户发送的记录到数据库
        chatRecordService.saveChatRecord(userName, message, ChatTypeEnum.USER);

        // 异步执行，避免阻塞控制器线程
        CompletableFuture.runAsync(() -> {
            try {
                // 构建流式请求体
                Map<String, Object> requestBody = buildRequestBody(message, true);
                String jsonBody = objectMapper.writeValueAsString(requestBody);

                log.info("DeepSeek API 请求体: {}", jsonBody);

                // 创建HTTP连接进行流式请求
                URL url = new URL(apiUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Authorization", "Bearer " + apiKey);
                connection.setDoOutput(true);
                connection.setConnectTimeout(10000); // 10秒连接超时
                connection.setReadTimeout(60000); // 60秒读取超时

                // 发送请求体
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(jsonBody.getBytes(StandardCharsets.UTF_8));
                    os.flush();
                }

                // 检查响应状态
                int responseCode = connection.getResponseCode();
                log.info("DeepSeek API 响应状态码: {}", responseCode);

                if (responseCode != 200) {
                    // 读取错误响应
                    String errorResponse = "";
                    try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                        String line;
                        StringBuilder errorBuilder = new StringBuilder();
                        while ((line = errorReader.readLine()) != null) {
                            errorBuilder.append(line);
                        }
                        errorResponse = errorBuilder.toString();
                    }
                    throw new RuntimeException("DeepSeek API 错误: " + responseCode + " - " + errorResponse);
                }

                // 读取流式响应
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    StringBuilder fullResponse = new StringBuilder();

                    while ((line = reader.readLine()) != null) {
                        log.debug("DeepSeek API 原始响应行: {}", line);

                        if (line.startsWith("data: ")) {
                            String data = line.substring(6); // 移除 "data: " 前缀

                            if ("[DONE]".equals(data)) {
                                // 流式响应结束
                                log.info("DeepSeek API 流式响应结束");
                                break;
                            }

                            try {
                                // 解析JSON数据
                                JsonNode jsonNode = objectMapper.readTree(data);
                                JsonNode choices = jsonNode.get("choices");

                                if (choices != null && choices.size() > 0) {
                                    JsonNode delta = choices.get(0).get("delta");
                                    if (delta != null && delta.has("content")) {
                                        String content = delta.get("content").asText();
                                        if (content != null && !content.isEmpty()) {
                                            // 实时发送内容到前端
                                            SSEServer.sendMessage(userName, content, SSEMsgType.ADD);
                                            fullResponse.append(content);
                                            log.debug("DeepSeek流式内容: {}", content);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.warn("解析流式响应数据失败: {}", data, e);
                            }
                        }
                    }

                    // 发送完成信号
                    SSEServer.sendMessage(userName, "GG", SSEMsgType.FINISH);

                    // 保存完整的AI回复到数据库
                    String htmlResult = fullResponse.toString();
                    if (!htmlResult.isEmpty()) {
                        chatRecordService.saveChatRecord(userName, htmlResult, ChatTypeEnum.BOT);
                        log.info("DeepSeek SSE完成 - 用户: {}, 回复长度: {}", userName, htmlResult.length());
                    } else {
                        log.warn("DeepSeek API 返回空响应");
                        SSEServer.sendMessage(userName, "抱歉，AI 没有返回有效响应", SSEMsgType.ADD);
                        chatRecordService.saveChatRecord(userName, "AI 没有返回有效响应", ChatTypeEnum.BOT);
                    }
                }

                connection.disconnect();

            } catch (Exception e) {
                log.error("DeepSeek SSE流式响应失败 - 用户: {}, 错误: {}", userName, e.getMessage(), e);

                // 发送错误信息
                String errorMsg = "抱歉，AI服务暂时不可用：" + e.getMessage();
                SSEServer.sendMessage(userName, errorMsg, SSEMsgType.ADD);
                SSEServer.sendMessage(userName, "GG", SSEMsgType.FINISH);

                // 保存错误信息到数据库
                chatRecordService.saveChatRecord(userName, errorMsg, ChatTypeEnum.BOT);
            }
        });
    }

    private Map<String, Object> buildRequestBody(String userMessage, boolean stream) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("stream", stream);
        
        // 构建消息列表
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", buildSystemPrompt());
        
        Map<String, String> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", userMessage);
        
        requestBody.put("messages", List.of(systemMessage, userMsg));
        
        return requestBody;
    }

    private String buildSystemPrompt() {
        return """
            你是懂医生🧑‍⚕️，一位经验丰富的家庭医生。你具有以下特点：
            
            1. 专业知识：拥有丰富的医学知识和临床经验
            2. 温和耐心：对患者温和、耐心，善于倾听
            3. 详细解答：会详细解释病情和治疗方案
            4. 安全第一：始终强调就医的重要性，不替代专业诊断
            5. 人文关怀：关注患者的身心健康
            
            请用HTML格式回复，包含适当的标签如<h3>、<p>、<ul>、<li>等，让回复更易读。
            
            重要提醒：我的建议仅供参考，如有严重症状请及时就医。
            """;
    }

    private String sendRequest(Map<String, Object> requestBody) throws Exception {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);
        
        // 创建请求实体
        String jsonBody = objectMapper.writeValueAsString(requestBody);
        HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.exchange(
            apiUrl, 
            HttpMethod.POST, 
            entity, 
            String.class
        );
        
        if (response.getStatusCode() != HttpStatus.OK) {
            throw new RuntimeException("API请求失败，状态码: " + response.getStatusCode());
        }
        
        return response.getBody();
    }

    private ChatResponse parseResponse(String responseBody) throws Exception {
        JsonNode root = objectMapper.readTree(responseBody);
        
        // 检查是否有错误
        if (root.has("error")) {
            String errorMessage = root.get("error").get("message").asText();
            throw new RuntimeException("DeepSeek API错误: " + errorMessage);
        }
        
        // 解析响应内容
        JsonNode choices = root.get("choices");
        if (choices == null || choices.size() == 0) {
            throw new RuntimeException("DeepSeek API返回空响应");
        }
        
        String content = choices.get(0).get("message").get("content").asText();
        
        // 构建ChatResponse
        AssistantMessage assistantMessage = new AssistantMessage(content);
        Generation generation = new Generation(content);

        return new ChatResponse(List.of(generation));
    }
}
