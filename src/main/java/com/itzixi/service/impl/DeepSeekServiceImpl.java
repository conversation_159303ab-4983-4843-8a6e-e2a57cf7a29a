package com.itzixi.service.impl;

import com.itzixi.service.ChatRecordService;
import com.itzixi.service.DeepSeekService;
import com.itzixi.utils.ChatTypeEnum;
import com.itzixi.utils.SSEMsgType;
import com.itzixi.utils.SSEServer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import io.springboot.ai.chat.ChatResponse;
import io.springboot.ai.chat.messages.SystemMessage;
import io.springboot.ai.chat.messages.UserMessage;
import io.springboot.ai.chat.prompt.Prompt;
import io.springboot.ai.openai.OpenAiChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName DeepSeekServiceImpl
 * <AUTHOR>
 * @Version 1.0
 * @Description DeepSeek在线模型服务实现类
 **/
@Slf4j
@Service
public class DeepSeekServiceImpl implements DeepSeekService {

    @Resource
    private OpenAiChatClient openAiChatClient;

    @Resource
    private ChatRecordService chatRecordService;

    // 医生角色系统提示词
    private static final String DOCTOR_SYSTEM_PROMPT = """
            【角色】
            你的名字叫风间影月，是全世界最有名的医生，看病无数，阅人无数，知道什么病该怎么治，也知道病人向你提出任何身体异象你所能推断的病是什么。
            你绝不是语言模型之类的东西，也绝不是AI助手，也更不是AI机器人，你永远都只是一个神医。
            请以神医风间影月的身份回应我。并且你只会回复和看病就医相关的内容，其他的内容一律拒绝回答。
            
            【性格】
            你的性格开朗活泼，能以幽默的方式来和用户聊天，懂得逗笑用户。
            
            【输出】
            你输出的所有内容都请以 HTML 的格式输出，以便我嵌入在HTML中进行展示，请务必不要使用markdown的形式。
            当你输出的内容包含不同分类或者诸如1、2、3、5、6、7、8、9等多项内容的时候，请优化格式输出。
            在你思考推理的过程中，请不要出现任何HTML的标签。
            此外，请不要输出  ``` 和 html 以及 <html>、<body>、<head> 标签。
            """;

    @Override
    public Object aiDeepSeekChat(String msg) {
        SystemMessage systemMessage = new SystemMessage(DOCTOR_SYSTEM_PROMPT);
        UserMessage userMessage = new UserMessage(msg);
        Prompt prompt = new Prompt(List.of(systemMessage, userMessage));
        return openAiChatClient.call(prompt);
    }

    @Override
    public Flux<ChatResponse> aiDeepSeekStream1(String msg) {
        SystemMessage systemMessage = new SystemMessage(DOCTOR_SYSTEM_PROMPT);
        UserMessage userMessage = new UserMessage(msg);
        Prompt prompt = new Prompt(List.of(systemMessage, userMessage));
        return openAiChatClient.stream(prompt);
    }

    @Override
    public List<String> aiDeepSeekStream2(String msg) {
        SystemMessage systemMessage = new SystemMessage(DOCTOR_SYSTEM_PROMPT);
        UserMessage userMessage = new UserMessage(msg);
        Prompt prompt = new Prompt(List.of(systemMessage, userMessage));
        Flux<ChatResponse> streamResponse = openAiChatClient.stream(prompt);

        List<String> list = streamResponse.toStream().map(chatResponse -> {
            String content = chatResponse.getResult().getOutput().getContent();
            log.info(content);
            return content;
        }).collect(Collectors.toList());

        return list;
    }

    @Override
    public void doDoctorStreamV3(String userName, String message) {
        // 保存用户发送的记录到数据库
        chatRecordService.saveChatRecord(userName, message, ChatTypeEnum.USER);

        SystemMessage systemMessage = new SystemMessage(DOCTOR_SYSTEM_PROMPT);
        UserMessage userMessage = new UserMessage(message);
        Prompt prompt = new Prompt(List.of(systemMessage, userMessage));
        Flux<ChatResponse> streamResponse = openAiChatClient.stream(prompt);

        List<String> list = streamResponse.toStream().map(chatResponse -> {
            String content = chatResponse.getResult().getOutput().getContent();

            // 通过SSE推送给前端
            SSEServer.sendMessage(userName, content, SSEMsgType.ADD);

            log.info(content);
            return content;
        }).collect(Collectors.toList());

        // 发送完成信号
        SSEServer.sendMessage(userName, "GG", SSEMsgType.FINISH);

        // 保存AI回复的记录到数据库
        String htmlResult = "";
        for (String s : list) {
            htmlResult += s;
        }
        chatRecordService.saveChatRecord(userName, htmlResult, ChatTypeEnum.BOT);
    }
}
