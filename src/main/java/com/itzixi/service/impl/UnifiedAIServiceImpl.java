package com.itzixi.service.impl;

import com.itzixi.service.ChatRecordService;
import com.itzixi.service.DeepSeekService;
import com.itzixi.service.OllamaService;
import com.itzixi.service.UnifiedAIService;
import com.itzixi.utils.ChatTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

/**
 * 统一AI服务实现
 */
@Slf4j
@Service
public class UnifiedAIServiceImpl implements UnifiedAIService {

    @Autowired
    private OllamaService ollamaService;

    @Autowired
    private DeepSeekService deepSeekService;

    @Resource
    private ChatRecordService chatRecordService;

    @Override
    public ChatResponse chat(String message, String modelType) {
        log.info("统一AI服务 - 同步聊天，模型类型: {}, 消息: {}", modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                // Ollama服务返回Object，需要转换
                Object ollamaResult = ollamaService.aiOllamaChat(message);
                // 这里需要根据实际返回类型进行转换，暂时抛出异常提示
                throw new UnsupportedOperationException("Ollama同步聊天需要适配返回类型");
            case "deepseek":
                return deepSeekService.chat(message);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek");
        }
    }

    @Override
    public ChatResponse chatAsync(String message, String modelType) {
        log.info("统一AI服务 - 异步聊天，模型类型: {}, 消息: {}", modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                // Ollama没有异步方法，使用同步方法
                return chat(message, modelType);
            case "deepseek":
                return deepSeekService.chatAsync(message);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek");
        }
    }

    @Override
    public Flux<ChatResponse> chatStream(String message, String modelType) {
        log.info("统一AI服务 - 流式聊天，模型类型: {}, 消息: {}", modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                return ollamaService.aiOllamaStream1(message);
            case "deepseek":
                return deepSeekService.chatStream(message);
            default:
                return Flux.error(new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek"));
        }
    }

    @Override
    public ChatResponse chatWithUser(String userName, String message, String modelType) {
        log.info("统一AI服务 - 带用户名聊天，用户: {}, 模型类型: {}, 消息: {}", userName, modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                // Ollama使用原有的流式方法，但需要适配
                log.warn("Ollama模型暂不支持统一的带用户名聊天，请使用原有接口");
                throw new UnsupportedOperationException("Ollama模型请使用 /ollama/ai/v3/doctor/stream 接口");
            case "deepseek":
                return deepSeekService.chatWithUser(userName, message);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek");
        }
    }

    @Override
    public ChatResponse chatAsyncWithUser(String userName, String message, String modelType) {
        log.info("统一AI服务 - 带用户名异步聊天，用户: {}, 模型类型: {}, 消息: {}", userName, modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                return chatWithUser(userName, message, modelType);
            case "deepseek":
                return deepSeekService.chatAsyncWithUser(userName, message);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek");
        }
    }

    @Override
    public Flux<ChatResponse> chatStreamWithUser(String userName, String message, String modelType) {
        log.info("统一AI服务 - 带用户名流式聊天，用户: {}, 模型类型: {}, 消息: {}", userName, modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                // 保存用户发送的记录到数据库
                chatRecordService.saveChatRecord(userName, message, ChatTypeEnum.USER);

                // 使用Ollama的流式方法
                Flux<ChatResponse> ollamaStream = ollamaService.aiOllamaStream1(message);

                // 收集所有响应并保存到数据库
                return ollamaStream.doOnComplete(() -> {
                    // 注意：这里简化处理，实际应该收集所有流式响应
                    log.info("Ollama流式响应完成，用户: {}", userName);
                });
            case "deepseek":
                return deepSeekService.chatStreamWithUser(userName, message);
            default:
                return Flux.error(new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek"));
        }
    }
}
