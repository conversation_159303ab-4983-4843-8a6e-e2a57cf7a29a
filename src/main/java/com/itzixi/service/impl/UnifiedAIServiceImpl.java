package com.itzixi.service.impl;

import com.itzixi.service.DeepSeekService;
import com.itzixi.service.OllamaService;
import com.itzixi.service.UnifiedAIService;
import com.itzixi.utils.ModelTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @ClassName UnifiedAIServiceImpl
 * <AUTHOR>
 * @Version 1.0
 * @Description 统一AI服务实现类，支持多模型切换
 **/
@Slf4j
@Service
public class UnifiedAIServiceImpl implements UnifiedAIService {

    @Resource
    private OllamaService ollamaService;

    @Resource
    private DeepSeekService deepSeekService;

    @Override
    public Object aiChat(String msg, String modelType) {
        ModelTypeEnum modelTypeEnum = ModelTypeEnum.getByType(modelType);
        
        switch (modelTypeEnum) {
            case DEEPSEEK:
                log.info("使用DeepSeek在线模型进行聊天");
                return deepSeekService.aiDeepSeekChat(msg);
            case OLLAMA:
            default:
                log.info("使用Ollama本地模型进行聊天");
                return ollamaService.aiOllamaChat(msg);
        }
    }

    @Override
    public Flux<ChatResponse> aiStream1(String msg, String modelType) {
        ModelTypeEnum modelTypeEnum = ModelTypeEnum.getByType(modelType);
        
        switch (modelTypeEnum) {
            case DEEPSEEK:
                log.info("使用DeepSeek在线模型进行流式聊天");
                return deepSeekService.aiDeepSeekStream1(msg);
            case OLLAMA:
            default:
                log.info("使用Ollama本地模型进行流式聊天");
                return ollamaService.aiOllamaStream1(msg);
        }
    }

    @Override
    public List<String> aiStream2(String msg, String modelType) {
        ModelTypeEnum modelTypeEnum = ModelTypeEnum.getByType(modelType);
        
        switch (modelTypeEnum) {
            case DEEPSEEK:
                log.info("使用DeepSeek在线模型进行流式聊天");
                return deepSeekService.aiDeepSeekStream2(msg);
            case OLLAMA:
            default:
                log.info("使用Ollama本地模型进行流式聊天");
                return ollamaService.aiOllamaStream2(msg);
        }
    }

    @Override
    public void doDoctorStream(String userName, String message, String modelType) {
        ModelTypeEnum modelTypeEnum = ModelTypeEnum.getByType(modelType);
        
        switch (modelTypeEnum) {
            case DEEPSEEK:
                log.info("使用DeepSeek在线模型进行医生咨询");
                deepSeekService.doDoctorStreamV3(userName, message);
                break;
            case OLLAMA:
            default:
                log.info("使用Ollama本地模型进行医生咨询");
                ollamaService.doDoctorStreamV3(userName, message);
                break;
        }
    }
}
