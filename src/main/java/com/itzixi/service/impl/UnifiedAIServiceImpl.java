package com.itzixi.service.impl;

import com.itzixi.service.DeepSeekService;
import com.itzixi.service.OllamaService;
import com.itzixi.service.UnifiedAIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

/**
 * 统一AI服务实现
 */
@Slf4j
@Service
public class UnifiedAIServiceImpl implements UnifiedAIService {

    @Autowired
    private OllamaService ollamaService;

    @Autowired
    private DeepSeekService deepSeekService;

    @Override
    public ChatResponse chat(String message, String modelType) {
        log.info("统一AI服务 - 同步聊天，模型类型: {}, 消息: {}", modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                // Ollama服务返回Object，需要转换
                Object ollamaResult = ollamaService.aiOllamaChat(message);
                // 这里需要根据实际返回类型进行转换，暂时抛出异常提示
                throw new UnsupportedOperationException("Ollama同步聊天需要适配返回类型");
            case "deepseek":
                return deepSeekService.chat(message);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek");
        }
    }

    @Override
    public ChatResponse chatAsync(String message, String modelType) {
        log.info("统一AI服务 - 异步聊天，模型类型: {}, 消息: {}", modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                // Ollama没有异步方法，使用同步方法
                return chat(message, modelType);
            case "deepseek":
                return deepSeekService.chatAsync(message);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek");
        }
    }

    @Override
    public Flux<ChatResponse> chatStream(String message, String modelType) {
        log.info("统一AI服务 - 流式聊天，模型类型: {}, 消息: {}", modelType, message);

        switch (modelType.toLowerCase()) {
            case "ollama":
                return ollamaService.aiOllamaStream1(message);
            case "deepseek":
                return deepSeekService.chatStream(message);
            default:
                return Flux.error(new IllegalArgumentException("不支持的模型类型: " + modelType + "，支持的类型: ollama, deepseek"));
        }
    }
}
