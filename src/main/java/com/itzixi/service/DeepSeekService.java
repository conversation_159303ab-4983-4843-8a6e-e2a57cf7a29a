package com.itzixi.service;

import io.springboot.ai.chat.ChatResponse;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @ClassName DeepSeekService
 * <AUTHOR>
 * @Version 1.0
 * @Description DeepSeek在线模型服务接口
 **/
public interface DeepSeekService {

    /**
     * 同步聊天
     * @param msg 用户消息
     * @return AI回复
     */
    Object aiDeepSeekChat(String msg);

    /**
     * 流式聊天 - 返回Flux
     * @param msg 用户消息
     * @return 流式响应
     */
    Flux<ChatResponse> aiDeepSeekStream1(String msg);

    /**
     * 流式聊天 - 返回List
     * @param msg 用户消息
     * @return 响应列表
     */
    List<String> aiDeepSeekStream2(String msg);

    /**
     * 医生流式聊天 - 带SSE推送
     * @param userName 用户名
     * @param message 用户消息
     */
    void doDoctorStreamV3(String userName, String message);
}
