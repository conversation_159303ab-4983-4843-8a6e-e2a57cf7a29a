package com.itzixi.service;

import org.springframework.ai.chat.ChatResponse;
import reactor.core.publisher.Flux;

/**
 * DeepSeek AI 服务接口
 */
public interface DeepSeekService {

    /**
     * 同步聊天
     * @param message 用户消息
     * @return 聊天响应
     */
    ChatResponse chat(String message);

    /**
     * 异步聊天
     * @param message 用户消息
     * @return 聊天响应
     */
    ChatResponse chatAsync(String message);

    /**
     * 流式聊天
     * @param message 用户消息
     * @return 流式响应
     */
    Flux<ChatResponse> chatStream(String message);

    /**
     * 带用户名的聊天（会保存到数据库）
     * @param userName 用户名
     * @param message 用户消息
     * @return 聊天响应
     */
    ChatResponse chatWithUser(String userName, String message);

    /**
     * 带用户名的异步聊天（会保存到数据库）
     * @param userName 用户名
     * @param message 用户消息
     * @return 聊天响应
     */
    ChatResponse chatAsyncWithUser(String userName, String message);

    /**
     * 带用户名的流式聊天（会保存到数据库）
     * @param userName 用户名
     * @param message 用户消息
     * @return 流式响应
     */
    Flux<ChatResponse> chatStreamWithUser(String userName, String message);

    /**
     * DeepSeek医生SSE流式响应（类似Ollama的doDoctorStreamV3）
     * 使用SSE实时推送响应内容到前端，支持实时显示
     * @param userName 用户名
     * @param message 用户消息
     */
    void deepSeekDoctorSSE(String userName, String message);
}
