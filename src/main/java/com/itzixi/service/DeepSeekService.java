package com.itzixi.service;

import org.springframework.ai.chat.ChatResponse;
import reactor.core.publisher.Flux;

/**
 * DeepSeek AI 服务接口
 */
public interface DeepSeekService {

    /**
     * 同步聊天
     * @param message 用户消息
     * @return 聊天响应
     */
    ChatResponse chat(String message);

    /**
     * 异步聊天
     * @param message 用户消息
     * @return 聊天响应
     */
    ChatResponse chatAsync(String message);

    /**
     * 流式聊天
     * @param message 用户消息
     * @return 流式响应
     */
    Flux<ChatResponse> chatStream(String message);
}
