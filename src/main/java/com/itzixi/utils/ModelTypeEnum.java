package com.itzixi.utils;

/**
 * @ClassName ModelTypeEnum
 * <AUTHOR>
 * @Version 1.0
 * @Description 模型类型枚举
 **/
public enum ModelTypeEnum {
    
    OLLAMA("ollama", "本地Ollama模型"),
    DEEPSEEK("deepseek", "DeepSeek在线模型");
    
    public final String type;
    public final String description;
    
    ModelTypeEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }
    
    public static ModelTypeEnum getByType(String type) {
        for (ModelTypeEnum modelType : ModelTypeEnum.values()) {
            if (modelType.type.equals(type)) {
                return modelType;
            }
        }
        return OLLAMA; // 默认返回Ollama
    }
}
