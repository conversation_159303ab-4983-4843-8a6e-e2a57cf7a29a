package com.itzixi.controller;

import com.itzixi.bean.ChatEntity;
import com.itzixi.service.ChatRecordService;
import com.itzixi.service.UnifiedAIService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 统一AI控制器，支持多种AI模型
 */
@Slf4j
@RestController
@RequestMapping("/ai")
public class UnifiedAIController {

    @Autowired
    private UnifiedAIService unifiedAIService;

    @Resource
    private ChatRecordService chatRecordService;

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public String test() {
        return "统一AI服务正常运行！支持模型: ollama, deepseek";
    }

    /**
     * 统一同步聊天接口
     */
    @PostMapping("/chat")
    public ChatResponse chat(
            @RequestParam String message,
            @RequestParam(defaultValue = "ollama") String model) {
        log.info("统一AI同步聊天请求 - 模型: {}, 消息: {}", model, message);
        ChatResponse response = unifiedAIService.chat(message, model);
        log.info("统一AI同步聊天响应: {}", response);
        return response;
    }

    /**
     * 统一异步聊天接口
     */
    @PostMapping("/chat/async")
    public ChatResponse chatAsync(
            @RequestParam String message,
            @RequestParam(defaultValue = "ollama") String model) {
        log.info("统一AI异步聊天请求 - 模型: {}, 消息: {}", model, message);
        ChatResponse response = unifiedAIService.chatAsync(message, model);
        log.info("统一AI异步聊天响应: {}", response);
        return response;
    }

    /**
     * 统一流式聊天接口
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatStream(
            @RequestParam String message,
            @RequestParam(defaultValue = "ollama") String model) {
        log.info("统一AI流式聊天请求 - 模型: {}, 消息: {}", model, message);
        return unifiedAIService.chatStream(message, model);
    }

    /**
     * 统一带用户名聊天接口（会保存到数据库）
     */
    @PostMapping("/chat/withUser")
    public ChatResponse chatWithUser(@RequestBody ChatEntity chatEntity) {
        log.info("统一AI带用户名聊天请求: {}", chatEntity);
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        String modelType = chatEntity.getModelType() != null ? chatEntity.getModelType() : "deepseek";
        return unifiedAIService.chatWithUser(userName, message, modelType);
    }

    /**
     * 统一带用户名异步聊天接口（会保存到数据库）
     */
    @PostMapping("/chat/async/withUser")
    public ChatResponse chatAsyncWithUser(@RequestBody ChatEntity chatEntity) {
        log.info("统一AI带用户名异步聊天请求: {}", chatEntity);
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        String modelType = chatEntity.getModelType() != null ? chatEntity.getModelType() : "deepseek";
        return unifiedAIService.chatAsyncWithUser(userName, message, modelType);
    }

    /**
     * 统一带用户名流式聊天接口（会保存到数据库）
     */
    @PostMapping(value = "/chat/stream/withUser", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatStreamWithUser(@RequestBody ChatEntity chatEntity) {
        log.info("统一AI带用户名流式聊天请求: {}", chatEntity);
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        String modelType = chatEntity.getModelType() != null ? chatEntity.getModelType() : "deepseek";
        return unifiedAIService.chatStreamWithUser(userName, message, modelType);
    }

    /**
     * 获取聊天记录
     */
    @GetMapping("/getRecords")
    public Object getChatRecords(@RequestParam String who) {
        log.info("获取聊天记录: {}", who);
        return chatRecordService.getChatRecordList(who);
    }
}
