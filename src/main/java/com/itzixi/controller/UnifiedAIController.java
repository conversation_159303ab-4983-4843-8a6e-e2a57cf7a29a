package com.itzixi.controller;

import com.itzixi.bean.ChatEntity;
import com.itzixi.service.ChatRecordService;
import com.itzixi.service.UnifiedAIService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import io.springboot.ai.chat.ChatResponse;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * @ClassName UnifiedAIController
 * <AUTHOR>
 * @Version 1.0
 * @Description 统一AI控制器，支持多模型切换
 **/
@Slf4j
@RestController
@RequestMapping("ai")
public class UnifiedAIController {

    @Resource
    private UnifiedAIService unifiedAIService;

    @Resource
    private ChatRecordService chatRecordService;

    /**
     * 统一聊天接口 - 同步
     * @param msg 用户消息
     * @param modelType 模型类型：ollama 或 deepseek，默认ollama
     * @return AI回复
     */
    @GetMapping("/chat")
    public Object aiChat(@RequestParam String msg, 
                        @RequestParam(defaultValue = "ollama") String modelType) {
        log.info("统一聊天接口，模型类型：{}，用户消息：{}", modelType, msg);
        return unifiedAIService.aiChat(msg, modelType);
    }

    /**
     * 统一流式聊天接口 - 返回Flux
     * @param msg 用户消息
     * @param modelType 模型类型：ollama 或 deepseek，默认ollama
     * @return 流式响应
     */
    @GetMapping("/stream1")
    public Flux<ChatResponse> aiStream1(@RequestParam String msg, 
                                       @RequestParam(defaultValue = "ollama") String modelType) {
        log.info("统一流式聊天1，模型类型：{}，用户消息：{}", modelType, msg);
        return unifiedAIService.aiStream1(msg, modelType);
    }

    /**
     * 统一流式聊天接口 - 返回List
     * @param msg 用户消息
     * @param modelType 模型类型：ollama 或 deepseek，默认ollama
     * @return 响应列表
     */
    @GetMapping("/stream2")
    public List<String> aiStream2(@RequestParam String msg, 
                                 @RequestParam(defaultValue = "ollama") String modelType) {
        log.info("统一流式聊天2，模型类型：{}，用户消息：{}", modelType, msg);
        return unifiedAIService.aiStream2(msg, modelType);
    }

    /**
     * 统一医生流式聊天接口 - 带SSE推送
     * @param chatEntity 聊天实体（包含modelType字段）
     */
    @PostMapping("/doctor/stream")
    public void aiDoctorStream(@RequestBody ChatEntity chatEntity) {
        log.info("统一医生咨询：{}", chatEntity.toString());
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        String modelType = chatEntity.getModelType() != null ? chatEntity.getModelType() : "ollama";

        unifiedAIService.doDoctorStream(userName, message, modelType);
    }

    /**
     * 获取聊天记录
     * @param who 用户名
     * @return 聊天记录
     */
    @GetMapping("/getRecords")
    public Object getChatRecords(@RequestParam String who) {
        return chatRecordService.getChatRecordList(who);
    }

    /**
     * 获取支持的模型类型
     * @return 模型类型列表
     */
    @GetMapping("/models")
    public Object getSupportedModels() {
        return List.of(
            Map.of("type", "ollama", "name", "本地Ollama模型", "description", "本地部署的DeepSeek模型"),
            Map.of("type", "deepseek", "name", "DeepSeek在线模型", "description", "DeepSeek官方在线API")
        );
    }
}
