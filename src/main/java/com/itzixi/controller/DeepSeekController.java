package com.itzixi.controller;

import com.itzixi.service.DeepSeekService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * DeepSeek AI 控制器
 */
@Slf4j
@RestController
@RequestMapping("/deepseek")
public class DeepSeekController {

    @Autowired
    private DeepSeekService deepSeekService;

    /**
     * 简单测试接口
     */
    @GetMapping("/test")
    public String test() {
        return "DeepSeek服务正常运行！";
    }

    /**
     * 同步聊天接口
     */
    @PostMapping("/chat")
    public ChatResponse chat(@RequestParam String message) {
        log.info("DeepSeek同步聊天请求: {}", message);
        ChatResponse response = deepSeekService.chat(message);
        log.info("DeepSeek同步聊天响应: {}", response);
        return response;
    }

    /**
     * 异步聊天接口
     */
    @PostMapping("/chat/async")
    public ChatResponse chatAsync(@RequestParam String message) {
        log.info("DeepSeek异步聊天请求: {}", message);
        ChatResponse response = deepSeekService.chatAsync(message);
        log.info("DeepSeek异步聊天响应: {}", response);
        return response;
    }

    /**
     * 流式聊天接口
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatStream(@RequestParam String message) {
        log.info("DeepSeek流式聊天请求: {}", message);
        return deepSeekService.chatStream(message);
    }
}
