package com.itzixi.controller;

import com.itzixi.bean.ChatEntity;
import com.itzixi.service.ChatRecordService;
import com.itzixi.service.DeepSeekService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * DeepSeek AI 控制器
 */
@Slf4j
@RestController
@RequestMapping("/deepseek")
public class DeepSeekController {

    @Autowired
    private DeepSeekService deepSeekService;

    @Resource
    private ChatRecordService chatRecordService;

    /**
     * 简单测试接口
     */
    @GetMapping("/test")
    public String test() {
        return "DeepSeek服务正常运行！";
    }

    /**
     * 同步聊天接口
     */
    @PostMapping("/chat")
    public ChatResponse chat(@RequestParam String message) {
        log.info("DeepSeek同步聊天请求: {}", message);
        ChatResponse response = deepSeekService.chat(message);
        log.info("DeepSeek同步聊天响应: {}", response);
        return response;
    }

    /**
     * 异步聊天接口
     */
    @PostMapping("/chat/async")
    public ChatResponse chatAsync(@RequestParam String message) {
        log.info("DeepSeek异步聊天请求: {}", message);
        ChatResponse response = deepSeekService.chatAsync(message);
        log.info("DeepSeek异步聊天响应: {}", response);
        return response;
    }

    /**
     * 流式聊天接口
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatStream(@RequestParam String message) {
        log.info("DeepSeek流式聊天请求: {}", message);
        return deepSeekService.chatStream(message);
    }

    /**
     * 带用户名的聊天接口（会保存到数据库）
     */
    @PostMapping("/chat/withUser")
    public ChatResponse chatWithUser(@RequestBody ChatEntity chatEntity) {
        log.info("DeepSeek带用户名聊天请求: {}", chatEntity);
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        return deepSeekService.chatWithUser(userName, message);
    }

    /**
     * 带用户名的异步聊天接口（会保存到数据库）
     */
    @PostMapping("/chat/async/withUser")
    public ChatResponse chatAsyncWithUser(@RequestBody ChatEntity chatEntity) {
        log.info("DeepSeek带用户名异步聊天请求: {}", chatEntity);
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        return deepSeekService.chatAsyncWithUser(userName, message);
    }

    /**
     * 带用户名的流式聊天接口（会保存到数据库）
     */
    @PostMapping(value = "/chat/stream/withUser", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatStreamWithUser(@RequestBody ChatEntity chatEntity) {
        log.info("DeepSeek带用户名流式聊天请求: {}", chatEntity);
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();
        return deepSeekService.chatStreamWithUser(userName, message);
    }

    /**
     * 获取聊天记录
     */
    @GetMapping("/getRecords")
    public Object getChatRecords(@RequestParam String who) {
        log.info("获取DeepSeek聊天记录: {}", who);
        return chatRecordService.getChatRecordList(who);
    }
}
