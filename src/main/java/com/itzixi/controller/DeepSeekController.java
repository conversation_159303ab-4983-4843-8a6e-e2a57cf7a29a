package com.itzixi.controller;

import com.itzixi.bean.ChatEntity;
import com.itzixi.service.ChatRecordService;
import com.itzixi.service.DeepSeekService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName DeepSeekController
 * <AUTHOR>
 * @Version 1.0
 * @Description DeepSeek在线模型控制器
 **/
@Slf4j
@RestController
@RequestMapping("deepseek")
public class DeepSeekController {

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Resource
    private DeepSeekService deepSeekService;

    @Resource
    private ChatRecordService chatRecordService;

    /**
     * DeepSeek同步聊天
     * @param msg 用户消息
     * @return AI回复
     */
    @GetMapping("/ai/chat")
    public Object aiDeepSeekChat(@RequestParam String msg) {
        log.info("DeepSeek同步聊天，用户消息：{}", msg);
        return deepSeekService.aiDeepSeekChat(msg);
    }

    /**
     * DeepSeek流式聊天 - 返回Flux
     * @param msg 用户消息
     * @return 流式响应
     */
    @GetMapping("/ai/stream1")
    public Flux<ChatResponse> aiDeepSeekStream1(@RequestParam String msg) {
        log.info("DeepSeek流式聊天1，用户消息：{}", msg);
        return deepSeekService.aiDeepSeekStream1(msg);
    }

    /**
     * DeepSeek流式聊天 - 返回List
     * @param msg 用户消息
     * @return 响应列表
     */
    @GetMapping("/ai/stream2")
    public List<String> aiDeepSeekStream2(@RequestParam String msg) {
        log.info("DeepSeek流式聊天2，用户消息：{}", msg);
        return deepSeekService.aiDeepSeekStream2(msg);
    }

    /**
     * DeepSeek医生流式聊天 - 带SSE推送
     * @param chatEntity 聊天实体
     */
    @PostMapping("/ai/doctor/stream")
    public void aiDeepSeekDoctorStream(@RequestBody ChatEntity chatEntity) {
        log.info("DeepSeek医生咨询：{}", chatEntity.toString());
        String userName = chatEntity.getCurrentUserName();
        String message = chatEntity.getMessage();

        deepSeekService.doDoctorStreamV3(userName, message);
    }

    /**
     * 获取聊天记录
     * @param who 用户名
     * @return 聊天记录
     */
    @GetMapping("/getRecords")
    public Object getDeepSeekChatRecords(@RequestParam String who) {
        return chatRecordService.getChatRecordList(who);
    }

    /**
     * 测试DeepSeek连接
     * @return 测试结果
     */
    @GetMapping("/test")
    public Object testDeepSeekConnection() {
        try {
            Prompt prompt = new Prompt(new UserMessage("你好，请简单介绍一下你自己"));
            return openAiChatModel.call(prompt);
        } catch (Exception e) {
            log.error("DeepSeek连接测试失败", e);
            return "DeepSeek连接失败：" + e.getMessage();
        }
    }
}
