package com.itzixi.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @ClassName ChatEntity
 * <AUTHOR>
 * @Version 1.0
 * @Description ChatEntity
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ChatEntity {
    private String currentUserName;
    private String message;
    private String modelType; // 模型类型：ollama 或 deepseek
}
